package com.volt.utils.render;

import lombok.experimental.UtilityClass;

@UtilityClass
public final class WaveUtils {

    public static float getWaveValue(float speed, float amplitude, float offset) {
        float time = (System.currentTimeMillis() % 10000) / 1000.0f;
        return amplitude * (float) Math.sin(time * speed + offset);
    }

    public static float getHueShift(float speed, float range, float offset) {
        float time = (System.currentTimeMillis() % 20000) / 1000.0f;
        return range * (float) Math.sin(time * speed + offset);
    }

    public static float getPulse(float speed, float min, float max, float offset) {
        float time = (System.currentTimeMillis() % 15000) / 1000.0f;
        float wave = (float) Math.sin(time * speed + offset);
        return min + (max - min) * (0.5f + 0.5f * wave);
    }

    public static float getTime() {
        return (System.currentTimeMillis() % 60000) / 1000.0f;
    }
} 