package com.volt.utils.render;

import lombok.Getter;
import lombok.experimental.UtilityClass;

import java.awt.*;
import java.util.HashMap;
import java.util.Map;

@UtilityClass
public final class ThemeUtils {

    public static final ColorScheme DARK_SCHEME = new ColorScheme(
            new Color(64, 158, 255),
            new Color(156, 39, 176),
            new Color(76, 175, 80),
            new Color(15, 15, 15, 180),
            new Color(25, 25, 25, 200),
            new Color(8, 8, 8, 220),
            new Color(255, 255, 255),
            new Color(180, 180, 180),
            new Color(120, 120, 120),
            new Color(60, 60, 60),
            new Color(80, 80, 80),
            new Color(30, 30, 30),
            new Color(76, 175, 80),
            new Color(255, 193, 7),
            new Color(244, 67, 54),
            new Color(33, 150, 243),
            new Color(0, 0, 0, 100),
            new Color(255, 255, 255, 30)
    );
    public static final ColorScheme LIGHT_SCHEME = new ColorScheme(
            new Color(25, 118, 210),
            new Color(123, 31, 162),
            new Color(56, 142, 60),
            new Color(250, 250, 250, 200),
            new Color(255, 255, 255, 220),
            new Color(240, 240, 240, 180),
            new Color(33, 33, 33),
            new Color(97, 97, 97),
            new Color(158, 158, 158),
            new Color(224, 224, 224),
            new Color(189, 189, 189),
            new Color(158, 158, 158),
            new Color(56, 142, 60),
            new Color(255, 160, 0),
            new Color(211, 47, 47),
            new Color(25, 118, 210),
            new Color(0, 0, 0, 50),
            new Color(255, 255, 255, 100)
    );
    public static final ColorScheme NEON_SCHEME = new ColorScheme(
            new Color(0, 255, 255),
            new Color(255, 0, 255),
            new Color(0, 255, 0),
            new Color(10, 10, 20, 200),
            new Color(20, 20, 40, 220),
            new Color(5, 5, 15, 240),
            new Color(255, 255, 255),
            new Color(200, 200, 255),
            new Color(120, 120, 150),
            new Color(0, 255, 255, 100),
            new Color(0, 255, 255, 150),
            new Color(0, 255, 255, 50),
            new Color(0, 255, 0),
            new Color(255, 255, 0),
            new Color(255, 0, 100),
            new Color(0, 255, 255),
            new Color(0, 255, 255, 30),
            new Color(255, 255, 255, 50)
    );
    public static final ColorScheme RETRO_SCHEME = new ColorScheme(
            new Color(255, 165, 0),
            new Color(255, 69, 0),
            new Color(255, 215, 0),
            new Color(139, 69, 19, 180),
            new Color(160, 82, 45, 200),
            new Color(101, 67, 33, 220),
            new Color(255, 248, 220),
            new Color(222, 184, 135),
            new Color(160, 160, 160),
            new Color(205, 133, 63),
            new Color(222, 184, 135),
            new Color(139, 69, 19),
            new Color(154, 205, 50),
            new Color(255, 215, 0),
            new Color(220, 20, 60),
            new Color(70, 130, 180),
            new Color(0, 0, 0, 80),
            new Color(255, 255, 224, 40)
    );
    public static final ColorScheme MINIMAL_SCHEME = new ColorScheme(
            new Color(96, 96, 96),
            new Color(64, 64, 64),
            new Color(128, 128, 128),
            new Color(248, 248, 248, 150),
            new Color(255, 255, 255, 180),
            new Color(240, 240, 240, 120),
            new Color(64, 64, 64),
            new Color(128, 128, 128),
            new Color(192, 192, 192),
            new Color(224, 224, 224),
            new Color(192, 192, 192),
            new Color(160, 160, 160),
            new Color(96, 96, 96),
            new Color(128, 128, 128),
            new Color(96, 96, 96),
            new Color(128, 128, 128),
            new Color(0, 0, 0, 30),
            new Color(255, 255, 255, 60)
    );
    public static final ColorScheme GAMING_SCHEME = new ColorScheme(
            new Color(255, 20, 147),
            new Color(138, 43, 226),
            new Color(0, 191, 255),
            new Color(25, 25, 25, 200),
            new Color(35, 35, 35, 220),
            new Color(15, 15, 15, 240),
            new Color(255, 255, 255),
            new Color(220, 220, 220),
            new Color(150, 150, 150),
            new Color(255, 20, 147, 80),
            new Color(255, 20, 147, 120),
            new Color(255, 20, 147, 40),
            new Color(50, 205, 50),
            new Color(255, 140, 0),
            new Color(255, 69, 0),
            new Color(0, 191, 255),
            new Color(255, 20, 147, 40),
            new Color(255, 255, 255, 40)
    );
    public static final ColorScheme PROFESSIONAL_SCHEME = new ColorScheme(
            new Color(21, 101, 192),
            new Color(67, 56, 202),
            new Color(16, 185, 129),
            new Color(17, 24, 39, 180),
            new Color(31, 41, 55, 200),
            new Color(9, 14, 24, 220),
            new Color(243, 244, 246),
            new Color(156, 163, 175),
            new Color(107, 114, 128),
            new Color(75, 85, 99),
            new Color(107, 114, 128),
            new Color(55, 65, 81),
            new Color(16, 185, 129),
            new Color(245, 158, 11),
            new Color(239, 68, 68),
            new Color(59, 130, 246),
            new Color(0, 0, 0, 60),
            new Color(255, 255, 255, 25)
    );
    public static final StylePreset MODERN_STYLE = new StylePreset(6, 4, 12, 16, 8, 4, 0.9f, 0.3f);
    public static final StylePreset ROUNDED_STYLE = new StylePreset(12, 6, 12, 18, 12, 8, 0.85f, 0.4f);
    public static final StylePreset SHARP_STYLE = new StylePreset(2, 2, 11, 14, 6, 2, 0.95f, 0.1f);
    public static final StylePreset SOFT_STYLE = new StylePreset(8, 8, 13, 20, 10, 6, 0.8f, 0.5f);
    public static final StylePreset COMPACT_STYLE = new StylePreset(4, 2, 10, 12, 4, 2, 0.9f, 0.2f);
    private static final Map<String, CustomTheme> customThemes = new HashMap<>();
    @Getter
    private static Theme currentTheme = Theme.DARK;

    public static void setTheme(Theme theme) {
        currentTheme = theme;
    }

    public static ColorScheme getCurrentColorScheme() {
        return switch (currentTheme) {
            case DARK -> DARK_SCHEME;
            case LIGHT -> LIGHT_SCHEME;
            case NEON -> NEON_SCHEME;
            case RETRO -> RETRO_SCHEME;
            case MINIMAL -> MINIMAL_SCHEME;
            case GAMING -> GAMING_SCHEME;
            case PROFESSIONAL -> PROFESSIONAL_SCHEME;
        };
    }

    public static StylePreset getCurrentStylePreset() {
        return switch (currentTheme) {
            case DARK, LIGHT, PROFESSIONAL -> MODERN_STYLE;
            case NEON, GAMING -> ROUNDED_STYLE;
            case RETRO -> SOFT_STYLE;
            case MINIMAL -> SHARP_STYLE;
        };
    }

    public static void registerCustomTheme(String name, ColorScheme colorScheme, StylePreset stylePreset) {
        customThemes.put(name, new CustomTheme(name, colorScheme, stylePreset));
    }

    public static CustomTheme getCustomTheme(String name) {
        return customThemes.get(name);
    }

    public static void applyCustomTheme(String name) {
        CustomTheme theme = customThemes.get(name);
        if (theme != null) {
            currentTheme = Theme.DARK;
        }
    }

    public static Color getPrimaryColor() {
        return getCurrentColorScheme().primary;
    }

    public static Color getSecondaryColor() {
        return getCurrentColorScheme().secondary;
    }

    public static Color getAccentColor() {
        return getCurrentColorScheme().accent;
    }

    public static Color getBackgroundColor() {
        return getCurrentColorScheme().background;
    }

    public static Color getBackgroundLightColor() {
        return getCurrentColorScheme().backgroundLight;
    }

    public static Color getBackgroundDarkColor() {
        return getCurrentColorScheme().backgroundDark;
    }

    public static Color getTextPrimaryColor() {
        return getCurrentColorScheme().textPrimary;
    }

    public static Color getTextSecondaryColor() {
        return getCurrentColorScheme().textSecondary;
    }

    public static Color getBorderColor() {
        return getCurrentColorScheme().border;
    }

    public static Color getSuccessColor() {
        return getCurrentColorScheme().success;
    }

    public static Color getWarningColor() {
        return getCurrentColorScheme().warning;
    }

    public static Color getErrorColor() {
        return getCurrentColorScheme().error;
    }

    public static Color getInfoColor() {
        return getCurrentColorScheme().info;
    }

    public static Color getShadowColor() {
        return getCurrentColorScheme().shadow;
    }

    public static Color getHighlightColor() {
        return getCurrentColorScheme().highlight;
    }

    public static int getBorderRadius() {
        return getCurrentStylePreset().borderRadius;
    }

    public static int getShadowSize() {
        return getCurrentStylePreset().shadowSize;
    }

    public static int getFontSize() {
        return getCurrentStylePreset().fontSize;
    }

    public static int getLineHeight() {
        return getCurrentStylePreset().lineHeight;
    }

    public static int getPadding() {
        return getCurrentStylePreset().padding;
    }

    public static int getMargin() {
        return getCurrentStylePreset().margin;
    }

    public static float getOpacity() {
        return getCurrentStylePreset().opacity;
    }

    public static float getGlowIntensity() {
        return getCurrentStylePreset().glowIntensity;
    }

    public static Color adjustBrightness(Color color, float factor) {
        int r = Math.max(0, Math.min(255, (int) (color.getRed() * factor)));
        int g = Math.max(0, Math.min(255, (int) (color.getGreen() * factor)));
        int b = Math.max(0, Math.min(255, (int) (color.getBlue() * factor)));
        return new Color(r, g, b, color.getAlpha());
    }

    public static Color adjustAlpha(Color color, int alpha) {
        return new Color(color.getRed(), color.getGreen(), color.getBlue(),
                Math.max(0, Math.min(255, alpha)));
    }

    public static Color blendColors(Color c1, Color c2, float ratio) {
        ratio = Math.max(0, Math.min(1, ratio));
        int r = (int) (c1.getRed() * (1 - ratio) + c2.getRed() * ratio);
        int g = (int) (c1.getGreen() * (1 - ratio) + c2.getGreen() * ratio);
        int b = (int) (c1.getBlue() * (1 - ratio) + c2.getBlue() * ratio);
        int a = (int) (c1.getAlpha() * (1 - ratio) + c2.getAlpha() * ratio);
        return new Color(r, g, b, a);
    }

    public static void styleButton(int x, int y, int width, int height, boolean hovered, boolean pressed) {
        ColorScheme colors = getCurrentColorScheme();
        StylePreset style = getCurrentStylePreset();

        Color bgColor = pressed ? colors.backgroundDark :
                hovered ? colors.backgroundLight : colors.background;
        Color borderColor = hovered ? colors.primary : colors.border;

    }

    public static void stylePanel(int x, int y, int width, int height) {
        ColorScheme colors = getCurrentColorScheme();
        StylePreset style = getCurrentStylePreset();

    }

    public static Color getAnimatedAccentColor(String animationId, float speed) {
        ColorScheme colors = getCurrentColorScheme();

        if (currentTheme == Theme.NEON || currentTheme == Theme.GAMING) {
            return GuiUtils.rainbow(AnimationUtils.pulse(animationId, speed), 0.8f, 1.0f);
        }

        return colors.accent;
    }

    public static Color getPulsingColor(String animationId, Color baseColor, float intensity) {
        float pulse = AnimationUtils.pulse(animationId, 2.0f);
        return adjustBrightness(baseColor, 1.0f + pulse * intensity);
    }

    public enum Theme {
        DARK, LIGHT, NEON, RETRO, MINIMAL, GAMING, PROFESSIONAL
    }

    public record ColorScheme(Color primary, Color secondary, Color accent, Color background, Color backgroundLight,
                              Color backgroundDark, Color textPrimary, Color textSecondary, Color textDisabled,
                              Color border, Color borderLight, Color borderDark, Color success, Color warning,
                              Color error, Color info, Color shadow, Color highlight) {
    }

    public record StylePreset(int borderRadius, int shadowSize, int fontSize, int lineHeight, int padding, int margin,
                              float opacity, float glowIntensity) {
    }

    public record CustomTheme(String name, ColorScheme colorScheme, StylePreset stylePreset) {
    }
} 