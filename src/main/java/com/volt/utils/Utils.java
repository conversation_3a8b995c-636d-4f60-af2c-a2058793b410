package com.volt.utils;

import com.volt.Volt;
import com.volt.module.modules.client.ClickGUIModule;
import lombok.experimental.UtilityClass;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.PlayerEntity;

import java.awt.*;

@UtilityClass
public final class Utils {
    public static Entity findNearestEntity(PlayerEntity toPlayer, float radius, boolean seeOnly) {
        float mr = Float.MAX_VALUE;
        Entity entity = null;

        assert Volt.mc.world != null;
        for (Entity e : Volt.mc.world.getEntities()) {
            float d = e.distanceTo(toPlayer);

            if (e != toPlayer && d <= radius && Volt.mc.player.canSee(e) == seeOnly) {
                if (d < mr) {
                    mr = d;
                    entity = e;
                }
            }
        }
        return entity;
    }

    public static Color getMainColor(int alpha) {
        int red = ClickGUIModule.red.getValueInt();
        int green = ClickGUIModule.green.getValueInt();
        int blue = ClickGUIModule.blue.getValueInt();

        return new Color(red, green, blue, alpha);
    }
}