package com.volt.utils.mc;

import lombok.experimental.UtilityClass;

import static com.volt.Volt.mc;

@UtilityClass
public final class MovementUtil {

    public static boolean isMoving() {
        return mc.options.forwardKey.isPressed() || mc.options.backKey.isPressed() || mc.options.leftKey.isPressed() || mc.options.rightKey.isPressed();
    }

    public static double getSpeed() {
        assert mc.player != null;
        return Math.hypot(mc.player.getVelocity().x, mc.player.getVelocity().z);
    }
}
