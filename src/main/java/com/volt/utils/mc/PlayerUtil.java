package com.volt.utils.mc;

import com.volt.IMinecraft;
import lombok.experimental.UtilityClass;

@UtilityClass
public final class PlayerUtil implements IMinecraft {
    private int offGroundTicks = 0;
    private int groundTicks = 0;

    public int getOffGroundTicks() {
        if (mc.player.isOnGround()) {
            groundTicks++;
            offGroundTicks = 0;
        } else {
            groundTicks = 0;
            offGroundTicks++;
        }
        return offGroundTicks;
    }

    public int getGroundTicks() {
        return groundTicks;
    }
}
