package com.volt.gui;

import com.volt.Volt;
import com.volt.module.Category;
import com.volt.module.modules.client.ClickGUIModule;
import com.volt.utils.render.RenderUtils;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;

import java.util.ArrayList;
import java.util.List;

import static com.volt.Volt.mc;

public final class ClickGui extends Screen {
    List<Frame> frames = new ArrayList<>();

    public ClickGui() {
        super(Text.empty());

        int offsetX = 40;
        for (Category category : Category.values()) {
            frames.add(new Frame(offsetX, 40, 240, 40, category));
            offsetX += 250;
        }

    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        RenderUtils.unscaledProjection();
        mouseX *= (int) MinecraftClient.getInstance().getWindow().getScaleFactor();
        mouseY *= (int) MinecraftClient.getInstance().getWindow().getScaleFactor();
        super.render(context, mouseX, mouseY, delta);

        for (Frame frame : frames) {
            frame.render(context, mouseX, mouseY, delta);
            frame.updatePosition(mouseX, mouseY);

            if (frame.dragging) {
                if (frame.getX() < 0) {
                    frame.setX(0);
                }

                if (frame.getY() < 0) {
                    frame.setY(0);
                }

                if (frame.getX() + frame.getWidth() > mc.getWindow().getWidth()) {
                    frame.setX(frame.getX() - frame.getWidth());
                }

                if (frame.getY() + frame.getHeight() > mc.getWindow().getHeight()) {
                    frame.setY(frame.getY() - frame.getHeight());
                }
            }
        }

        RenderUtils.scaledProjection();
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        mouseX *= (int) MinecraftClient.getInstance().getWindow().getScaleFactor();
        mouseY *= (int) MinecraftClient.getInstance().getWindow().getScaleFactor();

        for (Frame frame : frames) {
            frame.mouseClicked(mouseX, mouseY, button);
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        mouseX *= (int) MinecraftClient.getInstance().getWindow().getScaleFactor();
        mouseY *= (int) MinecraftClient.getInstance().getWindow().getScaleFactor();
        for (Frame frame : frames) {
            frame.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
        }
        return super.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
    }

    @Override
    public void close() {
        Volt.INSTANCE.getModuleManager().getModule(ClickGUIModule.class).setEnabled(false);
        super.close();
    }

    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        mouseX *= (int) MinecraftClient.getInstance().getWindow().getScaleFactor();
        mouseY *= (int) MinecraftClient.getInstance().getWindow().getScaleFactor();

        for (Frame frame : frames) {
            frame.mouseReleased(mouseX, mouseY, button);
        }

        return super.mouseReleased(mouseX, mouseY, button);
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        for (Frame frame : frames) {
            frame.keyPressed(keyCode, scanCode, modifiers);
        }
        return super.keyPressed(keyCode, scanCode, modifiers);
    }
}
