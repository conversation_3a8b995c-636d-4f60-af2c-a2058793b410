package com.volt.gui.components.settings;

import com.volt.Volt;
import com.volt.gui.components.ModuleButton;
import com.volt.module.setting.ModeSetting;
import com.volt.module.setting.Setting;
import com.volt.utils.font.FontManager;
import net.minecraft.client.gui.DrawContext;

import java.awt.*;

public final class ModeBox extends RenderableSetting {
    public final ModeSetting setting;

    public ModeBox(ModuleButton parent, Setting setting, int offset) {
        super(parent, setting, offset);
        this.setting = (ModeSetting) setting;
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        super.render(context, mouseX, mouseY, delta);

        Volt.INSTANCE.fontManager.getSize(20, FontManager.Type.Inter).drawString(context.getMatrices(), setting.getName() + ": " + setting.getMode(), parentX() + 6, (parentY() + offset + parentOffset() + 6), Color.WHITE);
    }

    @Override
    public void mouseClicked(double mouseX, double mouseY, int button) {
        if (isHovered(mouseX, mouseY)) {
            setting.cycle();
        }
        super.mouseClicked(mouseX, mouseY, button);
    }
}
