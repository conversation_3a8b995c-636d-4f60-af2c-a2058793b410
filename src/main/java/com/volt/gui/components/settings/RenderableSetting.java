package com.volt.gui.components.settings;

import com.volt.Volt;
import com.volt.gui.components.ModuleButton;
import com.volt.module.setting.Setting;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;

import java.awt.*;

@SuppressWarnings("all")
public abstract class RenderableSetting {
    public MinecraftClient mc = Volt.mc;
    public ModuleButton parent;
    public Setting setting;
    public boolean mouseOver;
    public int offset;
    int x;
    int y;
    int width;
    int height;

    public RenderableSetting(ModuleButton parent, Setting setting, int offset) {
        this.parent = parent;
        this.setting = setting;
        this.offset = offset;
    }

    public int parentX() {
        return parent.parent.getX();
    }

    public int parentY() {
        return parent.parent.getY();
    }

    public int parentWidth() {
        return parent.parent.getWidth();
    }

    public int parentHeight() {
        return parent.parent.getHeight();
    }

    public int parentOffset() {
        return parent.offset;
    }

    public void keyPressed(int keyCode, int scanCode, int modifiers) {
    }

    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        updateMouseOver(mouseX, mouseY);
        this.x = parentX();
        this.y = parentY() + parentOffset() + offset;
        this.width = parentX() + parentWidth();
        this.height = parentY() + parentOffset() + offset + parentHeight();

        context.fill(x, y, width, height, new Color(54, 57, 63, 153).getRGB());
    }

    private void updateMouseOver(double mouseX, double mouseY) {
        this.mouseOver = isHovered(mouseX, mouseY);
    }

    public boolean isHovered(double mouseX, double mouseY) {
        return mouseX > parentX()
                && mouseX < parentX() + parentWidth()
                && mouseY > offset + parentOffset() + parentY()
                && mouseY < offset + parentOffset() + parentY() + parentHeight();
    }

    public void mouseClicked(double mouseX, double mouseY, int button) {
    }

    public void mouseReleased(double mouseX, double mouseY, int button) {
    }

    public void mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
    }
}