package com.volt.gui.components.settings;

import com.volt.Volt;
import com.volt.gui.components.ModuleButton;
import com.volt.module.setting.BooleanSetting;
import com.volt.module.setting.Setting;
import com.volt.utils.Utils;
import com.volt.utils.font.FontManager;
import net.minecraft.client.gui.DrawContext;

import java.awt.*;

public final class CheckBox extends RenderableSetting {
    private final BooleanSetting setting;

    public CheckBox(ModuleButton parent, Setting setting, int offset) {
        super(parent, setting, offset);
        this.setting = (BooleanSetting) setting;
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        super.render(context, mouseX, mouseY, delta);

        // TextRenderer.drawMinecraftText(setting.getName(), context, parentX() + 6, (parentY() + parentOffset() + offset) + 6, Color.WHITE.getRGB());
        Volt.INSTANCE.fontManager.getSize(20, FontManager.Type.Inter).drawString(context.getMatrices(), setting.getName(), parentX() + 6, (parentY() + parentOffset() + offset) + 6, Color.WHITE);
        context.fillGradient(
                (parentX() + parentWidth()) - 30,
                (parentY() + parentOffset() + offset) + 5,
                (parentX() + parentWidth() - 10),
                (parentY() + parentOffset() + offset + parentHeight()) - 5,
                setting.getValue() ? Utils.getMainColor(255).brighter().getRGB() : Color.GRAY.getRGB(),
                setting.getValue() ? Utils.getMainColor(255).darker().getRGB() : Color.DARK_GRAY.getRGB()
        );
    }

    @Override
    public void mouseClicked(double mouseX, double mouseY, int button) {
        if (isHovered(mouseX, mouseY)) {
            setting.toggle();
        }
        super.mouseClicked(mouseX, mouseY, button);
    }
}
