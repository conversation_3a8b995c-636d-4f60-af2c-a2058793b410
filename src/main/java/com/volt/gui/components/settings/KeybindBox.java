package com.volt.gui.components.settings;

import com.volt.Volt;
import com.volt.gui.components.ModuleButton;
import com.volt.module.setting.KeybindSetting;
import com.volt.module.setting.Setting;
import com.volt.utils.font.FontManager;
import com.volt.utils.keybinding.KeyUtils;
import net.minecraft.client.gui.DrawContext;
import org.lwjgl.glfw.GLFW;

import java.awt.*;

public final class KeybindBox extends RenderableSetting {
    private final KeybindSetting keybind;

    public KeybindBox(ModuleButton parent, Setting setting, int offset) {
        super(parent, setting, offset);
        this.keybind = (KeybindSetting) setting;
    }

    @Override
    public void mouseClicked(double mouseX, double mouseY, int button) {
        if (isHovered(mouseX, mouseY)) {
            if (keybind.isListening()) {

                keybind.setKey(button);
                keybind.setListening(false);
            } else {
                if (button == GLFW.GLFW_MOUSE_BUTTON_LEFT) {
                    keybind.toggleListening();
                }
            }
        }
        super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public void keyPressed(int keyCode, int scanCode, int modifiers) {
        if (keybind.isListening()) {
            if (keyCode == GLFW.GLFW_KEY_ESCAPE) {
                keybind.setListening(false);
            } else if (keyCode == GLFW.GLFW_KEY_BACKSPACE) {
                keybind.setKey(keybind.getOriginalKey());
                keybind.setListening(false);
            } else {
                keybind.setKey(keyCode);
                keybind.setListening(false);
            }
        }
        super.keyPressed(keyCode, scanCode, modifiers);
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        super.render(context, mouseX, mouseY, delta);

        int off = parentX() + 6;
        int yPos = parentY() + parentOffset() + offset + 9;

        if (!keybind.isListening()) {
            Volt.INSTANCE.fontManager.getSize(20, FontManager.Type.Inter).drawString(context.getMatrices(), setting.getName() + ": " + KeyUtils.getKey(keybind.getKey()), off, yPos, Color.WHITE);
        } else {
            Volt.INSTANCE.fontManager.getSize(20, FontManager.Type.Inter).drawString(context.getMatrices(), "Press a key...", off, yPos, Color.WHITE);
        }
    }
}
