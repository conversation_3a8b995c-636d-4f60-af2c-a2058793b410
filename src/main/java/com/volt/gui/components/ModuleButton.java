package com.volt.gui.components;

import com.volt.Volt;
import com.volt.gui.Frame;
import com.volt.gui.components.settings.*;
import com.volt.module.Module;
import com.volt.module.setting.*;
import com.volt.utils.Utils;
import com.volt.utils.font.FontManager;
import net.minecraft.client.gui.DrawContext;

import java.awt.*;
import java.util.ArrayList;
import java.util.List;

public final class ModuleButton {
    public List<RenderableSetting> settings = new ArrayList<>();
    public Frame parent;
    public Module module;
    public int offset;
    public boolean extended;
    public int settingOffset;

    public ModuleButton(Frame parent, Module module, int offset) {
        this.parent = parent;
        this.module = module;
        this.offset = offset;
        this.extended = false;

        settingOffset = parent.getHeight();
        for (Setting setting : module.getSettings()) {
            if (setting instanceof BooleanSetting booleanSetting) {
                settings.add(new CheckBox(this, booleanSetting, settingOffset));
            } else if (setting instanceof NumberSetting numberSetting) {
                settings.add(new Slider(this, numberSetting, settingOffset));
            } else if (setting instanceof ModeSetting modeSetting) {
                settings.add(new ModeBox(this, modeSetting, settingOffset));
            } else if (setting instanceof KeybindSetting keybindSetting) {
                settings.add(new KeybindBox(this, keybindSetting, settingOffset));
            }
            settingOffset += parent.getHeight();
        }
    }


    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        context.fill(parent.getX(), parent.getY() + offset, parent.getX() + parent.getWidth(), parent.getY() + parent.getHeight() + offset, new Color(54, 57, 63, 153).getRGB());
        Color textColor = new Color(module.isEnabled() ? Utils.getMainColor(255).getRGB() : Color.WHITE.getRGB());
        Volt.INSTANCE.fontManager.getSize(20, FontManager.Type.Inter)
                .drawCenteredString(context.getMatrices(), module.getName(),
                        parent.getX() + (parent.getWidth() / 2), parent.getY() + offset + 8,
                        textColor);


        if (isHovered(mouseX, mouseY)) {
            context.fill(parent.getX(), parent.getY() + offset, parent.getX() + parent.getWidth(), parent.getY() + parent.getHeight() + offset, new Color(255, 255, 255, 10).getRGB());
        }

        if (extended) {
            for (RenderableSetting renderableSetting : settings) {
                renderableSetting.render(context, mouseX, mouseY, delta);
            }
        }
    }

    public void mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        if (extended) {
            for (RenderableSetting renderableSetting : settings) {
                renderableSetting.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
            }
        }
    }

    public void mouseClicked(double mouseX, double mouseY, int button) {
        if (isHovered(mouseX, mouseY)) {
            if (button == 0) {
                module.toggle();
            }

            if (button == 1) {
                extended = !extended;
                parent.updateButtons();
            }
        }
        if (extended) {
            for (RenderableSetting renderableSetting : settings) {
                renderableSetting.mouseClicked(mouseX, mouseY, button);
            }
        }
    }

    public void mouseReleased(double mouseX, double mouseY, int button) {
        for (RenderableSetting renderableSetting : settings) {
            renderableSetting.mouseReleased(mouseX, mouseY, button);
        }
    }

    public void keyPressed(int keyCode, int scanCode, int modifiers) {
        for (RenderableSetting renderableSetting : settings) {
            renderableSetting.keyPressed(keyCode, scanCode, modifiers);
        }
    }

    public boolean isHovered(double mouseX, double mouseY) {
        return mouseX > parent.getX()
                && mouseX < parent.getX() + parent.getWidth()
                && mouseY > parent.getY() + offset
                && mouseY < parent.getY() + offset + parent.getHeight();
    }
}
