package com.volt;

import com.volt.event.EventManager;
import com.volt.module.ModuleManager;
import com.volt.profiles.ProfileManager;
import com.volt.utils.font.FontManager;
import lombok.Getter;
import net.fabricmc.api.ModInitializer;
import net.minecraft.client.MinecraftClient;

@Getter
public final class Volt implements ModInitializer {
    public static Volt INSTANCE;
    public static MinecraftClient mc;
    public final EventManager eventManager;
    public final ModuleManager moduleManager;
    public final FontManager fontManager;
    public final ProfileManager profileManager;

    public Volt() {
        INSTANCE = this;
        mc = MinecraftClient.getInstance();
        eventManager = new EventManager();
        moduleManager = new ModuleManager();
        fontManager = new FontManager();
        profileManager = new ProfileManager();
    }

    @Override
    public void onInitialize() {
        new Volt();
    }
}