package com.volt.module.modules.client;

import com.volt.gui.ClickGui;
import com.volt.module.Category;
import com.volt.module.Module;
import com.volt.module.setting.NumberSetting;
import org.lwjgl.glfw.GLFW;

public final class ClickGUIModule extends Module {
    public static final NumberSetting red = new NumberSetting("Red", 0, 255, 15, 1);
    public static final NumberSetting green = new NumberSetting("Green", 0, 255, 115, 1);
    public static final NumberSetting blue = new NumberSetting("Blue", 0, 255, 225, 1);

    public ClickGUIModule() {
        super("Click Gui", "Click Gui", GLFW.GLFW_KEY_RIGHT_SHIFT, Category.CLIENT);
        addSettings(red, green, blue);
    }

    @Override
    public void onEnable() {
        mc.setScreen(new ClickGui());
        super.onEnable();
    }

    @Override
    public void onDisable() {
        if (mc.currentScreen instanceof ClickGui) {
            mc.setScreen(null);
        }
        super.onDisable();
    }
}
