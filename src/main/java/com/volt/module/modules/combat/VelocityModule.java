package com.volt.module.modules.combat;

import com.volt.event.bus.Listener;
import com.volt.event.bus.annotations.EventLink;
import com.volt.event.impl.player.TickEvent;
import com.volt.module.Category;
import com.volt.module.Module;
import com.volt.module.setting.BooleanSetting;
import com.volt.module.setting.NumberSetting;

public final class VelocityModule extends Module {
    public static final NumberSetting chance = new NumberSetting("Chance (%)", 1, 100, 100, 1);
    public static final BooleanSetting noScreen = new BooleanSetting("Ignore Containers", true);
    public static final BooleanSetting ignoreWhenBackwards = new BooleanSetting("Ignore S press", true);

    public VelocityModule() {
        super("Velocity", "Modifies your velocity when you take damage", -1, Category.COMBAT);
        this.addSettings(chance, noScreen, ignoreWhenBackwards);
    }

    @EventLink
    private final Listener<TickEvent> onTick = event -> {
        if (isNull()) return;
        if (noScreen.getValue() && mc.currentScreen != null) return;
        if (!chanceCheck()) return;
        if (ignoreWhenBackwards.getValue() && mc.options.backKey.isPressed()) return;
        if (mc.player.hurtTime == 9 && mc.player.isOnGround()) {
            mc.player.jump();
        }
    };

    private boolean chanceCheck() {
        return (Math.random() * 100) < chance.getValueFloat();
    }
}
