package com.volt.module.modules.combat;

import com.volt.event.bus.Listener;
import com.volt.event.bus.annotations.EventLink;
import com.volt.event.impl.player.AttackEvent;
import com.volt.module.Category;
import com.volt.module.Module;
import net.minecraft.util.hit.HitResult;

public final class AntiMissModule extends Module {
    public AntiMissModule() {
        super("Anti Miss", "Makes you not miss", -1, Category.COMBAT);
    }

    @EventLink
    public final Listener<AttackEvent> eventListener = attackEvent -> {
        if (isNull()) return;

        assert mc.crosshairTarget != null;
        if (mc.crosshairTarget.getType().equals(HitResult.Type.MISS)) {
            attackEvent.cancel();
        }
    };
}
