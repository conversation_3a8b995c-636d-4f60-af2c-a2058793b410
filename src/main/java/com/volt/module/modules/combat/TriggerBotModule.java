package com.volt.module.modules.combat;

import com.volt.event.bus.Listener;
import com.volt.event.bus.annotations.EventLink;
import com.volt.event.impl.player.TickEvent;
import com.volt.event.impl.world.WorldChangeEvent;
import com.volt.mixin.MinecraftClientAccessor;
import com.volt.module.Category;
import com.volt.module.Module;
import com.volt.module.setting.BooleanSetting;
import com.volt.module.setting.NumberSetting;
import com.volt.utils.math.MathUtils;
import com.volt.utils.math.TimerUtil;
import com.volt.utils.mc.MovementUtil;
import net.minecraft.entity.Entity;
import net.minecraft.entity.Tameable;
import net.minecraft.entity.decoration.EndCrystalEntity;
import net.minecraft.entity.passive.PassiveEntity;
import net.minecraft.item.AxeItem;
import net.minecraft.item.Item;
import net.minecraft.item.SwordItem;

/**
 * <AUTHOR>
 */

public final class TriggerBotModule extends Module {
    public static final NumberSetting swordMaxMS = new NumberSetting("Sword Max", 1, 1200, 545, 0.5f);
    public static final NumberSetting swordMinMS = new NumberSetting("Sword Min", 1, 1200, 520, 0.5);
    public static final NumberSetting axeThresholdMax = new NumberSetting("Axe Threshold Max", 0.1, 1, 0.95, 0.01);
    public static final NumberSetting axeThresholdMin = new NumberSetting("Axe Threshold Min", 0.1, 1, 0.90, 0.01);
    public static final NumberSetting axePostDelayMax = new NumberSetting("Axe Post Max", 1, 500, 120, 0.5);
    public static final NumberSetting axePostDelayMin = new NumberSetting("Axe Post Min", 1, 500, 120, 0.5);
    public static final BooleanSetting preferCrits = new BooleanSetting("Prefer Crits", false);
    public static final BooleanSetting ignorePassiveMobs = new BooleanSetting("No Passive", true);
    public static final BooleanSetting ignoreInvisible = new BooleanSetting("No Invisible", true);
    public static final BooleanSetting ignoreCrystals = new BooleanSetting("No Crystals", true);
    public static final BooleanSetting useOnlySwordOrAxe = new BooleanSetting("Only Sword/Axe", true);
    public static final BooleanSetting onlyWhenMouseDown = new BooleanSetting("Only Mouse Hold", false);
    public static final BooleanSetting disableOnWorldChange = new BooleanSetting("Disable on Load", false);
    public static final BooleanSetting samePlayer = new BooleanSetting("Same Player", false);
    private final TimerUtil timer = new TimerUtil();
    private final TimerUtil samePlayerTimer = new TimerUtil();
    private final TimerUtil timerSword = new TimerUtil();
    public boolean waitingForDelay = false;
    float swordDelay;
    float randomizedPostDelay;
    float randomizedThreshold;
    private Entity target;
    private String lastTargetUUID = null;

    public TriggerBotModule() {
        super("Trigger Bot", "Makes you automatically attack once aimed at a target", -1, Category.COMBAT);
        addSettings(
                swordMaxMS, swordMinMS, axeThresholdMax, axeThresholdMin, axePostDelayMax, axePostDelayMin, ignorePassiveMobs,
                ignoreCrystals, preferCrits, ignoreInvisible, onlyWhenMouseDown, useOnlySwordOrAxe, disableOnWorldChange, samePlayer
        );
    }

    @EventLink
    public final Listener<WorldChangeEvent> eventListener1 = worldChangeEvent -> {
        if (disableOnWorldChange.getValue() && this.isEnabled()) {
            this.toggle();
            System.out.println("Disabled due to world change!");
        }
    };

    @EventLink
    public final Listener<TickEvent> eventListener = tickEvent -> {
        if (mc.player == null || mc.world == null || mc.player.isUsingItem()) return;
        if (axeThresholdMin.getValueFloat() >= axeThresholdMax.getValueFloat()) {
            axeThresholdMin.setValue(axeThresholdMax.getValueFloat() - 0.05f);
        }

        if (swordMinMS.getValueFloat() >= swordMaxMS.getValueFloat()) {
            swordMinMS.setValue(swordMaxMS.getValueFloat() - 0.5f);
        }

        if (axePostDelayMin.getValueFloat() >= axePostDelayMax.getValueFloat()) {
            axePostDelayMin.setValue(axePostDelayMax.getValueFloat() - 0.5f);
        }


        target = mc.targetedEntity;
        if (target == null) return;


        if (!hasElapsedDelay()) return;
        if (!isHoldingSwordOrAxe()) return;
        if (onlyWhenMouseDown.getValue() && !mc.options.attackKey.isPressed()) return;


        if (hasTarget(target) && samePlayerCheck(target)) {
            attack();
        }
    };

    public boolean hasTarget(Entity en) {
        if (en == mc.player || en == mc.cameraEntity || !en.isAlive()) return false;
        return switch (en) {
            case EndCrystalEntity endCrystalEntity when ignoreCrystals.getValue() -> false;
            case Tameable tameable -> false;
            case PassiveEntity passiveEntity when ignorePassiveMobs.getValue() -> false;
            default -> !ignoreInvisible.getValue() || !en.isInvisible();
        };
    }

    private boolean setPreferCrits() {
        if (!preferCrits.getValue()) {
            return false;
        }
        assert mc.player != null;
        boolean isFalling = mc.player.fallDistance > 0 && !mc.player.isOnGround() && mc.player.getVelocity().y < 0;

        return isFalling && !MovementUtil.isMoving();
    }

    private boolean samePlayerCheck(Entity entity) {
        if (!samePlayer.getValue()) return true;
        if (entity == null) return false;

        if (samePlayerTimer.hasElapsedTime(3000, false)) {
            lastTargetUUID = null;
            return true;
        }

        return entity.getUuidAsString().equals(lastTargetUUID);
    }

    private boolean hasElapsedDelay() {
        Item heldItem = mc.player.getMainHandStack().getItem();
        float cooldown = mc.player.getAttackCooldownProgress(0.0f);

        if (heldItem instanceof AxeItem) {
            randomizedThreshold = (float) MathUtils.randomDoubleBetween(axeThresholdMin.getValueFloat(), axeThresholdMax.getValueFloat());
            randomizedPostDelay = (float) MathUtils.randomDoubleBetween(axePostDelayMin.getValueFloat(), axePostDelayMax.getValueFloat());
        } else {
            if (preferCrits.getValue() && setPreferCrits()) {
                swordDelay += 50;
            }
            swordDelay = (float) MathUtils.randomDoubleBetween(swordMinMS.getValueFloat(), swordMaxMS.getValueFloat());
            return timerSword.hasElapsedTime((long) swordDelay, true);
        }
        if (cooldown >= randomizedThreshold) {
            if (!waitingForDelay) {
                timer.reset();
                waitingForDelay = true;
            }
            return timer.hasElapsedTime((long) randomizedPostDelay, true);
        } else {
            waitingForDelay = false;
            return false;
        }
    }

    private boolean isHoldingSwordOrAxe() {
        if (!useOnlySwordOrAxe.getValue()) return true;
        assert mc.player != null;
        Item item = mc.player.getMainHandStack().getItem();
        return item instanceof AxeItem || item instanceof SwordItem;
    }

    private void attack() {
        ((MinecraftClientAccessor) mc).invokeDoAttack();

        if (samePlayer.getValue() && target != null) {
            lastTargetUUID = target.getUuidAsString();
            samePlayerTimer.reset();
        }
    }

    @Override
    public void onEnable() {
        timerSword.reset();
        timer.reset();
        super.onEnable();
    }

    @Override
    public void onDisable() {
        timerSword.reset();
        timer.reset();
        super.onDisable();
    }
}
