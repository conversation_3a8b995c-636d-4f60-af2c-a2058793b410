package com.volt.module.modules.misc;

import com.volt.event.bus.Listener;
import com.volt.event.bus.annotations.EventLink;
import com.volt.event.impl.player.TickEvent;
import com.volt.module.Category;
import com.volt.module.Module;
import net.minecraft.entity.Entity;
import net.minecraft.entity.decoration.ArmorStandEntity;

public final class AntiTrapModule extends Module {

    public AntiTrapModule() {
        super("Anti Trap", "Removes entities used for trapping (armor stands)", -1, Category.MISC);
    }

    @EventLink
    public final Listener<TickEvent> onTick = event -> {
        if (isNull()) return;

        for (Entity en : mc.world.getEntities()) {
            if (en instanceof ArmorStandEntity) {
                en.remove(Entity.RemovalReason.DISCARDED);
            }
        }
    };

    @Override
    public void onEnable() {
        super.onEnable();
    }

    @Override
    public void onDisable() {
        super.onDisable();
    }
}
