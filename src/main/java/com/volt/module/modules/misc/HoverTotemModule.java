package com.volt.module.modules.misc;

import com.volt.event.bus.Listener;
import com.volt.event.bus.annotations.EventLink;
import com.volt.event.impl.player.TickEvent;
import com.volt.mixin.HandledScreenMixin;
import com.volt.module.Category;
import com.volt.module.Module;
import com.volt.module.setting.BooleanSetting;
import com.volt.module.setting.NumberSetting;
import com.volt.utils.math.TimerUtil;
import net.minecraft.client.gui.screen.ingame.InventoryScreen;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.screen.slot.Slot;
import net.minecraft.screen.slot.SlotActionType;

import java.util.Random;

public final class HoverTotemModule extends Module {
    public static final NumberSetting minMS = new NumberSetting("Min MS", 1, 1000, 200, 0.5);
    public static final NumberSetting maxMS = new NumberSetting("Max MS", 1, 1000, 400, 0.5);
    public static final BooleanSetting ignoreOffhand = new BooleanSetting("Ignore Offhand", false);

    private final TimerUtil timer = new TimerUtil();
    private final Random random = new Random();

    public HoverTotemModule() {
        super("Hover Totem", "Puts a totem in your offhand once hovered", -1, Category.MISC);
        this.addSettings(minMS, maxMS, ignoreOffhand);
    }

    @EventLink
    public final Listener<TickEvent> onTick = event -> {
        if (isNull()) return;

        if (minMS.getValueFloat() >= maxMS.getValueFloat()) {
            minMS.setValue(maxMS.getValueFloat() - 0.5);
        }

        ItemStack offhandItem = mc.player.getOffHandStack();
        if (!offhandItem.isEmpty() && ignoreOffhand.getValue()) return;
        if (offhandItem.getItem() == Items.TOTEM_OF_UNDYING) return;
        totemHandler();
    };

    private void totemHandler() {
        if (!(mc.currentScreen instanceof InventoryScreen inv)) return;

        Slot focusedSlot = ((HandledScreenMixin) inv).getFocusedSlot();
        if (focusedSlot == null) return;

        ItemStack focusedItemStack = focusedSlot.getStack();
        if (focusedItemStack.getItem() != Items.TOTEM_OF_UNDYING) return;

        int minDelay = minMS.getValueInt();
        int maxDelay = maxMS.getValueInt();

        if (minDelay >= maxDelay) {
            maxDelay = minDelay + 1;
        }

        long delay = random.nextLong(minDelay, maxDelay);
        if (timer.hasElapsedTime(delay, true)) {
            assert mc.player != null;
            assert mc.interactionManager != null;

            mc.interactionManager.clickSlot(
                    mc.player.currentScreenHandler.syncId,
                    focusedSlot.getIndex(),
                    40,
                    SlotActionType.SWAP,
                    mc.player
            );
        }
    }

    @Override
    public void onEnable() {
        timer.reset();
        super.onEnable();
    }

    @Override
    public void onDisable() {
        timer.reset();
        super.onDisable();
    }
}
