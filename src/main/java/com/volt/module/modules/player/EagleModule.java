package com.volt.module.modules.player;

import com.volt.event.bus.Listener;
import com.volt.event.bus.annotations.EventLink;
import com.volt.event.impl.render.EventRender2D;
import com.volt.module.Category;
import com.volt.module.Module;
import net.minecraft.block.Blocks;
import net.minecraft.util.math.BlockPos;

public final class EagleModule extends Module {

    public EagleModule() {
        super("Eagle", "Sneaks at block edge", -1, Category.PLAYER);
    }

    @EventLink
    public final Listener<EventRender2D> eventRender2DListener = eventRender2D -> {
        if (isNull() || !mc.player.isOnGround()) {
            mc.options.sneakKey.setPressed(false);
            return;
        }

        double x = mc.player.getX();
        double y = mc.player.getY() - 0.1;
        double z = mc.player.getZ();

        boolean onEdge =
                isAir(x + 0.1, y, z + 0.1) ||
                        isAir(x - 0.1, y, z + 0.1) ||
                        isAir(x + 0.1, y, z - 0.1) ||
                        isAir(x - 0.1, y, z - 0.1);

        mc.options.sneakKey.setPressed(onEdge);
    };

    private boolean isAir(double x, double y, double z) {
        BlockPos pos = new BlockPos((int) Math.floor(x), (int) Math.floor(y), (int) Math.floor(z));
        assert mc.world != null;
        return mc.world.getBlockState(pos).getBlock() == Blocks.AIR;
    }

    @Override
    public void onDisable() {
        mc.options.sneakKey.setPressed(false);
    }
}
