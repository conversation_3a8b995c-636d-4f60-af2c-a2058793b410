package com.volt.module.modules.player;

import com.volt.event.bus.Listener;
import com.volt.event.bus.annotations.EventLink;
import com.volt.event.impl.network.EventPacket;
import com.volt.module.Category;
import com.volt.module.Module;
import com.volt.module.setting.NumberSetting;
import net.minecraft.network.packet.c2s.common.KeepAliveC2SPacket;
import net.minecraft.network.packet.s2c.common.KeepAliveS2CPacket;

import java.util.concurrent.CompletableFuture;

public final class PingSpoofModule extends Module {
    private final NumberSetting msDelay = new NumberSetting("Ms", 1, 500, 60, 1);

    public PingSpoofModule() {
        super("PingSpoof", "Increases your ping", -1, Category.PLAYER);
        this.addSetting(msDelay);
    }

    @EventLink
    public final Listener<EventPacket> packetEvent = eventPacket -> {
        if (eventPacket.getPacket() instanceof KeepAliveS2CPacket packet) {
            if (isNull()) return;
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    Thread.sleep(msDelay.getValueInt());
                    mc.getNetworkHandler().getConnection().send(new KeepAliveC2SPacket(packet.getId()));
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            });
            future.join();
        }
    };
}
