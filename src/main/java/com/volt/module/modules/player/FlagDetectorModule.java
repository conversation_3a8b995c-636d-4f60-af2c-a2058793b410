package com.volt.module.modules.player;

import com.volt.event.bus.Listener;
import com.volt.event.bus.annotations.EventLink;
import com.volt.event.impl.network.EventPacket;
import com.volt.event.types.TransferOrder;
import com.volt.module.Category;
import com.volt.module.Module;
import com.volt.utils.mc.ChatUtils;
import net.minecraft.network.packet.s2c.play.PlayerPositionLookS2CPacket;

public final class FlagDetectorModule extends Module {

    public FlagDetectorModule() {
        super("Flag Detector", "Detects flags", -1, Category.PLAYER);
    }

    @EventLink
    public final Listener<EventPacket> eventPacketListener = event -> {
        if (isNull()) return;

        if (mc.player.age < 40) return;
        if (event.getOrder() == TransferOrder.RECEIVE) {
            if (event.getPacket() instanceof PlayerPositionLookS2CPacket) {
                ChatUtils.addChatMessage("Flag detected!");
            }
        }
    };
}
