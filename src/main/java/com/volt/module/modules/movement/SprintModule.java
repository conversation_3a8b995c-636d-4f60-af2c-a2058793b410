package com.volt.module.modules.movement;

import com.volt.event.bus.Listener;
import com.volt.event.bus.annotations.EventLink;
import com.volt.event.impl.player.TickEvent;
import com.volt.module.Category;
import com.volt.module.Module;

public final class SprintModule extends Module {

    public SprintModule() {
        super("Sprint", "Makes you automatically sprint", -1, Category.MOVEMENT);
    }

    @EventLink
    public final Listener<TickEvent> eventListener = tickEvent -> {
        if (isNull()) return;
        mc.options.sprintKey.setPressed(true);
    };

    @Override
    public void onEnable() {
        super.onEnable();
    }

    @Override
    public void onDisable() {
        super.onDisable();
    }
}
