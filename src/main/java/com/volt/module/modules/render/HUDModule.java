package com.volt.module.modules.render;

import com.volt.Volt;
import com.volt.event.bus.Listener;
import com.volt.event.bus.annotations.EventLink;
import com.volt.event.impl.render.EventRender2D;
import com.volt.gui.ClickGui;
import com.volt.module.Category;
import com.volt.module.Module;
import com.volt.module.setting.BooleanSetting;
import com.volt.module.setting.ModeSetting;
import com.volt.module.setting.NumberSetting;
import com.volt.utils.font.FontManager;
import com.volt.utils.render.AnimationUtils;
import com.volt.utils.render.WaveUtils;
import net.minecraft.client.gui.screen.ingame.InventoryScreen;
import net.minecraft.client.util.math.MatrixStack;

import java.awt.*;
import java.util.ArrayList;
import java.util.List;

public final class HUDModule extends Module {
    public static final ModeSetting fontMode = new ModeSetting("Font", "Inter", "Inter", "MC");
    public static final BooleanSetting ignoreVisuals = new BooleanSetting("Ignore Visuals", false);
    public static final NumberSetting red = new NumberSetting("Red", 0, 255, 15, 1);
    public static final NumberSetting green = new NumberSetting("Green", 0, 255, 115, 1);
    public static final NumberSetting blue = new NumberSetting("Blue", 0, 255, 225, 1);
    public static final NumberSetting yLevel = new NumberSetting("Y", 0, 50, 0, 1);
    public static final NumberSetting xLevel = new NumberSetting("X", 0, 50, 0, 1);

    public static final BooleanSetting animations = new BooleanSetting("Animations", true);
    public static final BooleanSetting slideAnimation = new BooleanSetting("Slide Animation", true);
    public static final BooleanSetting colorAnimation = new BooleanSetting("Color Animation", true);
    public static final ModeSetting colorMode = new ModeSetting("Color Mode", "Wave", "Wave", "Pulse", "Fade", "Rainbow", "Breathing");
    public static final BooleanSetting scaleAnimation = new BooleanSetting("Scale Animation", true);
    public static final BooleanSetting sortByLength = new BooleanSetting("Sort by Length", true);
    public static final NumberSetting animationSpeed = new NumberSetting("Animation Speed", 100, 1000, 300, 50);
    public static final NumberSetting waveIntensity = new NumberSetting("Wave Intensity", 0.1, 1.0, 0.3, 0.1);
    public static final BooleanSetting smoothReordering = new BooleanSetting("Smooth Reordering", true);

    public static final BooleanSetting showInfoBox = new BooleanSetting("Show Info Box", true);
    public static final ModeSetting infoBoxPosition = new ModeSetting("Info Box Position", "Top Right", "Top Left", "Top Right");

    public static final BooleanSetting showShadow = new BooleanSetting("Show Shadow", true);

    private final List<AnimatedModule> animatedModules = new ArrayList<>();

    public HUDModule() {
        super("HUD", "Displays your modules with smooth animations", -1, Category.RENDER);
        this.addSettings(fontMode, ignoreVisuals, red, green, blue, yLevel, xLevel,
                animations, slideAnimation, colorAnimation, colorMode, scaleAnimation,
                sortByLength, animationSpeed, waveIntensity, smoothReordering,
                showInfoBox, infoBoxPosition, showShadow);
    }

    @EventLink
    public final Listener<EventRender2D> render = event -> {
        if (isNull() || mc.currentScreen instanceof InventoryScreen || mc.currentScreen instanceof ClickGui) {
            return;
        }

        updateAnimatedModules();
        renderAnimatedArraylist(event);

        if (showInfoBox.getValue()) {
            renderInfoBox(event);
        }
    };

    private void updateAnimatedModules() {
        List<Module> enabledModules = new ArrayList<>();

        for (Module module : Volt.INSTANCE.getModuleManager().getModules()) {
            if (!module.isEnabled()) continue;
            if (ignoreVisuals.getValue() && module.getModuleCategory() == Category.RENDER) continue;
            enabledModules.add(module);
        }

        if (sortByLength.getValue()) {
            enabledModules.sort((a, b) -> {
                int lengthA = getModuleWidth(a);
                int lengthB = getModuleWidth(b);
                return Integer.compare(lengthB, lengthA);
            });
        }

        for (int i = 0; i < enabledModules.size(); i++) {
            Module module = enabledModules.get(i);
            AnimatedModule animatedModule = getOrCreateAnimatedModule(module);
            animatedModule.targetIndex = i;

            if (!animatedModule.shouldBeVisible) {
                animatedModule.shouldBeVisible = true;
            }

            if (smoothReordering.getValue()) {
                animatedModule.updateTargetPosition(yLevel.getValueInt() + i * getLineHeight());
            }
        }

        animatedModules.removeIf(animatedModule -> {
            boolean isCurrentlyEnabled = enabledModules.contains(animatedModule.module);

            if (!isCurrentlyEnabled && animatedModule.shouldBeVisible) {
                animatedModule.shouldBeVisible = false;
            }

            if (!animatedModule.shouldBeVisible) {
                float alpha = AnimationUtils.animate(animatedModule.module.getName() + "_alpha",
                        0.0f,
                        animationSpeed.getValueInt(),
                        AnimationUtils.Easing.EASE_OUT_QUAD);
                return alpha <= 0.01f;
            }
            return false;
        });
    }

    private void renderAnimatedArraylist(EventRender2D event) {
        int baseX = xLevel.getValueInt();
        MatrixStack stack = event.getContext().getMatrices();

        if (showShadow.getValue() && !animatedModules.isEmpty()) {
            renderArraylistShadow(event, baseX);
        }

        for (AnimatedModule animatedModule : animatedModules) {
            renderAnimatedModule(stack, animatedModule, baseX, event);
        }
    }

    private void renderAnimatedModule(MatrixStack stack, AnimatedModule animatedModule, int baseX, EventRender2D event) {
        Module module = animatedModule.module;
        String moduleName = module.getName();

        if (!animations.getValue()) {
            int staticY = yLevel.getValueInt() + animatedModule.targetIndex * getLineHeight();
            renderStaticModule(stack, module, baseX, staticY, event);
            return;
        }

        float alpha = AnimationUtils.animate(moduleName + "_alpha",
                animatedModule.shouldBeVisible ? 1.0f : 0.0f,
                animationSpeed.getValueInt(),
                AnimationUtils.Easing.EASE_OUT_QUAD);
        if (alpha <= 0.01f) return;

        int renderX = baseX;
        int renderY = (int) animatedModule.getCurrentY();

        if (slideAnimation.getValue()) {
            float slideOffset = AnimationUtils.animate(moduleName + "_slide",
                    animatedModule.shouldBeVisible ? 0 : -getModuleWidth(module),
                    animationSpeed.getValueInt(),
                    AnimationUtils.Easing.EASE_OUT_CUBIC);
            renderX = baseX + (int) slideOffset;
        }


        renderModuleWithEffects(stack, module, renderX, renderY, alpha, animatedModule, event);
    }

    private void renderModuleWithEffects(MatrixStack stack, Module module, int x, int y, float alpha, AnimatedModule animatedModule, EventRender2D event) {
        String moduleName = module.getName();

        float scale = 1.0f;
        if (scaleAnimation.getValue()) {
            scale = AnimationUtils.animate(moduleName + "_scale",
                    module.isEnabled() ? 1.0f : 0.8f,
                    animationSpeed.getValueInt(),
                    AnimationUtils.Easing.EASE_OUT_BACK);
        }

        Color baseColor = new Color(red.getValueInt(), green.getValueInt(), blue.getValueInt());
        Color finalColor;

        if (colorAnimation.getValue()) {
            finalColor = getAnimatedColor(baseColor, animatedModule, alpha);
        } else {
            finalColor = new Color(baseColor.getRed(), baseColor.getGreen(), baseColor.getBlue(), (int) (alpha * 255));
        }


        stack.push();
        stack.translate(x, y, 0);
        stack.scale(scale, scale, 1.0f);

        renderText(stack, moduleName, finalColor, event);

        stack.pop();
    }

    private void renderStaticModule(MatrixStack stack, Module module, int x, int y, EventRender2D event) {
        Color color = new Color(red.getValueInt(), green.getValueInt(), blue.getValueInt());

        if (fontMode.isMode("Inter")) {
            Volt.INSTANCE.fontManager
                    .getSize(10, FontManager.Type.Inter)
                    .drawString(stack, module.getName(), x + 4, y + 2, color);
        } else {
            event.getContext().drawTextWithShadow(mc.textRenderer, module.getName(), x + 4, y + 2, color.getRGB());
        }
    }

    private AnimatedModule getOrCreateAnimatedModule(Module module) {
        return animatedModules.stream()
                .filter(am -> am.module == module)
                .findFirst()
                .orElseGet(() -> {
                    AnimatedModule newAnimated = new AnimatedModule(module);
                    animatedModules.add(newAnimated);
                    return newAnimated;
                });
    }

    private int getModuleWidth(Module module) {
        if (fontMode.isMode("Inter")) {
            return (int) Volt.INSTANCE.fontManager
                    .getSize(10, FontManager.Type.Inter)
                    .getStringWidth(module.getName());
        } else {
            return mc.textRenderer.getWidth(module.getName());
        }
    }

    private int getLineHeight() {
        return 14;
    }

    private Color getAnimatedColor(Color baseColor, AnimatedModule animatedModule, float alpha) {
        float time = WaveUtils.getTime();
        float modulePosition = animatedModule.targetIndex * getLineHeight();

        return switch (colorMode.getMode()) {
            case "Wave" -> getWaveColor(baseColor, time, modulePosition, alpha);
            case "Pulse" -> getPulseColor(baseColor, time, alpha);
            case "Fade" -> getFadeColor(baseColor, time, modulePosition, alpha);
            case "Rainbow" -> getRainbowColor(time, modulePosition, alpha);
            case "Breathing" -> getBreathingColor(baseColor, time, alpha);
            default -> new Color(baseColor.getRed(), baseColor.getGreen(), baseColor.getBlue(), (int) (alpha * 255));
        };
    }

    private Color getWaveColor(Color baseColor, float time, float modulePosition, float alpha) {
        float waveOffset = modulePosition * 0.05f;
        float speed = (1000.0f - animationSpeed.getValueInt()) / 1000.0f * 3.0f;
        float waveValue = (float) Math.sin(time * speed - waveOffset);
        float brightness = waveIntensity.getValueFloat() + (1.0f - waveIntensity.getValueFloat()) *
                (0.5f + 0.5f * waveValue);

        int r = (int) (baseColor.getRed() * brightness);
        int g = (int) (baseColor.getGreen() * brightness);
        int b = (int) (baseColor.getBlue() * brightness);

        return new Color(
                Math.min(255, Math.max(0, r)),
                Math.min(255, Math.max(0, g)),
                Math.min(255, Math.max(0, b)),
                (int) (alpha * 255)
        );
    }

    private Color getPulseColor(Color baseColor, float time, float alpha) {
        float speed = (1000.0f - animationSpeed.getValueInt()) / 1000.0f * 3.0f;
        float pulse = 0.5f + 0.5f * (float) Math.sin(time * speed);
        float brightness = waveIntensity.getValueFloat() + (1.0f - waveIntensity.getValueFloat()) * pulse;

        int r = (int) (baseColor.getRed() * brightness);
        int g = (int) (baseColor.getGreen() * brightness);
        int b = (int) (baseColor.getBlue() * brightness);

        return new Color(
                Math.min(255, Math.max(0, r)),
                Math.min(255, Math.max(0, g)),
                Math.min(255, Math.max(0, b)),
                (int) (alpha * 255)
        );
    }

    private Color getFadeColor(Color baseColor, float time, float modulePosition, float alpha) {
        float fadeOffset = modulePosition * 0.02f;
        float speed = (1000.0f - animationSpeed.getValueInt()) / 1000.0f * 3.0f;
        float fadeValue = (float) Math.sin(time * speed - fadeOffset);
        float finalAlpha = alpha * (waveIntensity.getValueFloat() + (1.0f - waveIntensity.getValueFloat()) *
                (0.5f + 0.5f * fadeValue));

        return new Color(baseColor.getRed(), baseColor.getGreen(), baseColor.getBlue(), (int) (finalAlpha * 255));
    }

    private Color getRainbowColor(float time, float modulePosition, float alpha) {
        float speed = (1000.0f - animationSpeed.getValueInt()) / 1000.0f * 3.0f;
        float hue = (time * speed * 0.1f + modulePosition * 0.01f) % 1.0f;
        float saturation = 0.8f + 0.2f * (float) Math.sin(time * 2.0f);
        float brightness = waveIntensity.getValueFloat() + (1.0f - waveIntensity.getValueFloat()) *
                (0.7f + 0.3f * (float) Math.sin(time * 1.5f));

        Color rainbowColor = Color.getHSBColor(hue, saturation, brightness);
        return new Color(rainbowColor.getRed(), rainbowColor.getGreen(), rainbowColor.getBlue(), (int) (alpha * 255));
    }

    private Color getBreathingColor(Color baseColor, float time, float alpha) {
        float speed = (1000.0f - animationSpeed.getValueInt()) / 1000.0f * 3.0f;
        float breathe = (float) Math.sin(time * speed * 0.5f);
        float brightness = waveIntensity.getValueFloat() + (1.0f - waveIntensity.getValueFloat()) *
                (0.5f + 0.5f * breathe);

        int r = (int) (baseColor.getRed() * brightness);
        int g = (int) (baseColor.getGreen() * brightness);
        int b = (int) (baseColor.getBlue() * brightness);

        return new Color(
                Math.min(255, Math.max(0, r)),
                Math.min(255, Math.max(0, g)),
                Math.min(255, Math.max(0, b)),
                (int) (alpha * 255)
        );
    }

    private void renderInfoBox(EventRender2D event) {
        if (!showInfoBox.getValue()) return;

        String text = "Volt" +
                " | " +
                "Dev Build" +
                " | " +
                mc.getCurrentFps() + "fps";
        MatrixStack stack = event.getContext().getMatrices();

        int textWidth = getTextWidth(text);
        int textHeight = 10;
        int padding = 6;
        int boxWidth = textWidth + (padding * 2);
        int boxHeight = textHeight + (padding * 2);

        int screenWidth = event.getWidth();

        int boxX, boxY;
        if (infoBoxPosition.isMode("Top Left")) {
            boxX = 5;
        } else {
            boxX = screenWidth - boxWidth - 5;
        }
        boxY = 5;

        Color backgroundColor = new Color(0, 0, 0, 120);
        Color textColor = new Color(red.getValueInt(), green.getValueInt(), blue.getValueInt());

        event.getContext().fill(boxX, boxY, boxX + boxWidth, boxY + boxHeight, backgroundColor.getRGB());

        int textX = boxX + padding;
        int textY = boxY + padding;

        if (fontMode.isMode("Inter")) {
            Volt.INSTANCE.fontManager
                    .getSize(10, FontManager.Type.Inter)
                    .drawString(stack, text, textX, textY, textColor);
        } else {
            event.getContext().drawTextWithShadow(mc.textRenderer, text, textX, textY, textColor.getRGB());
        }
    }

    private int getTextWidth(String text) {
        if (fontMode.isMode("Inter")) {
            return (int) Volt.INSTANCE.fontManager
                    .getSize(10, FontManager.Type.Inter)
                    .getStringWidth(text);
        } else {
            return mc.textRenderer.getWidth(text);
        }
    }

    private void renderText(MatrixStack stack, String text, Color color, EventRender2D event) {
        if (fontMode.isMode("Inter")) {
            Volt.INSTANCE.fontManager
                    .getSize(10, FontManager.Type.Inter)
                    .drawString(stack, text, 4, 2, color);
        } else {
            event.getContext().drawTextWithShadow(mc.textRenderer, text, 4, 2, color.getRGB());
        }
    }

    private void renderArraylistShadow(EventRender2D event, int baseX) {
        int offset = 3;
        int shadowAlpha = 120;
        Color shadowColor = new Color(0, 0, 0, shadowAlpha);

        for (AnimatedModule animatedModule : animatedModules) {
            if (!animatedModule.shouldBeVisible) continue;

            int moduleY = (int) animatedModule.getCurrentY();
            int moduleWidth = getModuleWidth(animatedModule.module);
            int moduleHeight = getLineHeight();

            int shadowX = baseX + offset - 3;
            int shadowY = moduleY + offset;
            int shadowWidth = moduleWidth + 6;

            event.getContext().fill(shadowX, shadowY, shadowX + shadowWidth, shadowY + moduleHeight, shadowColor.getRGB());
        }
    }

    @Override
    public void onDisable() {
        AnimationUtils.clearAnimations();
        animatedModules.clear();
        super.onDisable();
    }

    private static class AnimatedModule {
        final Module module;
        int targetIndex;
        boolean shouldBeVisible;
        private float currentY;
        private float targetY;

        AnimatedModule(Module module) {
            this.module = module;
            this.targetIndex = 0;
            this.shouldBeVisible = false;
            this.currentY = 0;
            this.targetY = 0;
        }

        void updateTargetPosition(float newTargetY) {
            this.targetY = newTargetY;
        }

        float getCurrentY() {
            if (!shouldBeVisible) {
                return targetY;
            }

            String animId = module.getName() + "_position";
            this.currentY = AnimationUtils.animate(animId, targetY,
                    250,
                    AnimationUtils.Easing.EASE_OUT_CUBIC);
            return currentY;
        }
    }
}