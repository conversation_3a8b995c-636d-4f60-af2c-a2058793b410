# ===================
# Java & JVM files
# ===================
*.class
*.jar
*.war
*.ear
*.zip
*.log
*.hprof
*.jfr
*.idx
*.bin
*.dat
*.map
*.rej
*.orig
*.patch
*.tmp
*.swp

# Crash and error logs
hs_err_*.log
replay_*.log

# ===================
# Build outputs
# ===================
/build/
/out/
/classes/
/bin/

# ===================
# Gradle
# ===================
.gradle/
!gradle/wrapper/gradle-wrapper.jar

# ===================
# Maven
# ===================
/target/

# ===================
# IDEs and Editors
# ===================

# Eclipse
.classpath
.project
.settings/
*.launch

# IntelliJ IDEA
.idea/
*.iml
*.ipr
*.iws

# VS Code
.vscode/

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# ===================
# OS & Filesystem
# ===================

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.lnk

# Linux
*~
.nfs*

# ===================
# Miscellaneous Tools
# ===================

# Fabric (Minecraft modding)
run/

# Fabric Loom cache files
.fabric/
.loom-cache/
remapClasspath.txt

# Logs
logs/
*.log

# Local environment files
.env
.env.local
.env.*.local

# JetBrains Rider
.idea/

# ===================
# Git
# ===================
*.orig
*.patch

/.gradle/
